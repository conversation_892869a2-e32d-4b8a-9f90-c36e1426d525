import EmptyImage from '@/assets/chat/fallbackImage.svg';
import { Text2ImgModelState } from '@/models/text2Img';
import ImgMasonry from '@/pages/Office/components/ImgMasonry';
import {
  deleteTextImageHistory,
  fetchTextImageHistory,
  ImagesType,
  TextImageMenuList,
} from '@/services/text2Img';
import { convertTextToRichInput, dispatchInUtils } from '@/utils';
import { TabPane, Tabs, Toast } from '@douyinfe/semi-ui';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { ImageItemType } from '../components/ImgMasonry';
import styles from './index.less';
interface Text2ImgProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}
const Text2Img: React.FC<Text2ImgProps> = ({ activeTab, onTabChange }) => {
  const { navList } = useSelector((state: { text2Img: Text2ImgModelState }) => state.text2Img);
  const text2ImgRef = useRef<HTMLDivElement | null>(null);
  const [images, setImages] = useState<ImageItemType[]>([]);
  const [loading, setLoading] = useState(false);
  const [containerHeight, setContainerHeight] = useState(0);
  const dispatch = useDispatch();
  const [currentTabId, setCurrentTabId] = useState<string>('');

  // 虚拟滚动相关状态
  const [visibleImages, setVisibleImages] = useState<ImageItemType[]>([]);
  const [batchSize] = useState(20); // 每批渲染的数量
  const renderTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 滚动性能优化
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isScrolling, setIsScrolling] = useState(false);

  // 当前渲染的tab标识，用于防止tab切换时的数据混乱
  const currentRenderingTabRef = useRef<string>('');

  // 优化的分批渲染函数，支持虚拟滚动和性能优化
  const renderImagesBatch = React.useCallback(
    (allImages: ImageItemType[], immediate = false, tabId?: string) => {
      // 设置当前渲染的tab标识
      if (tabId) {
        currentRenderingTabRef.current = tabId;
      }

      // 清除之前的定时器
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }

      // 如果没有图片，直接返回
      if (allImages.length === 0) {
        setVisibleImages([]);
        return;
      }

      // 立即渲染模式：直接渲染前20条，避免loading状态
      if (immediate) {
        const initialBatch = allImages.slice(0, batchSize);
        setVisibleImages(initialBatch);

        // 如果还有更多图片，使用 requestIdleCallback 在空闲时渲染
        if (allImages.length > batchSize) {
          const renderRemaining = (batchIndex: number) => {
            // 检查是否还是当前tab，如果已切换则停止渲染
            if (tabId && currentRenderingTabRef.current !== tabId) {
              return;
            }

            const endIndex = batchIndex * batchSize;
            const batchImages = allImages.slice(0, endIndex);

            setVisibleImages(batchImages);

            if (endIndex < allImages.length) {
              // 使用 requestIdleCallback 优化性能
              if (window.requestIdleCallback) {
                window.requestIdleCallback(
                  () => {
                    // 再次检查tab是否切换
                    if (tabId && currentRenderingTabRef.current === tabId) {
                      renderRemaining(batchIndex + 1);
                    }
                  },
                  { timeout: 100 },
                );
              } else {
                renderTimerRef.current = setTimeout(() => {
                  // 再次检查tab是否切换
                  if (tabId && currentRenderingTabRef.current === tabId) {
                    renderRemaining(batchIndex + 1);
                  }
                }, 32); // 降低频率，减少卡顿
              }
            }
          };

          // 延迟开始后续渲染，让首批渲染完成
          renderTimerRef.current = setTimeout(() => {
            // 检查tab是否还是当前的
            if (tabId && currentRenderingTabRef.current === tabId) {
              renderRemaining(2);
            }
          }, 100);
        }
        return;
      }

      // 普通分批渲染逻辑（用于删除等操作）
      setVisibleImages([]);
      const renderBatch = (batchIndex: number) => {
        // 检查是否还是当前tab
        if (tabId && currentRenderingTabRef.current !== tabId) {
          return;
        }

        const endIndex = batchIndex * batchSize;
        const batchImages = allImages.slice(0, endIndex);

        setVisibleImages(batchImages);

        if (endIndex < allImages.length) {
          renderTimerRef.current = setTimeout(() => {
            // 再次检查tab是否切换
            if (tabId && currentRenderingTabRef.current === tabId) {
              renderBatch(batchIndex + 1);
            }
          }, 32); // 降低渲染频率
        }
      };

      renderBatch(1);
    },
    [batchSize],
  );

  // useEffect(() => {
  //   dispatchInUtils({
  //     type: 'text2Img/fetchTextImageMenuList',
  //   });
  // }, []);

  useEffect(() => {
    if (navList?.length) {
      onTabChange(String(navList[0].routeId));
      const richTextValue = convertTextToRichInput(navList[0]?.prompt || '');
      dispatchInUtils({
        type: 'chat/setRichTextContent',
        payload: {
          useRichText: true,
          richTextValue,
        },
      });
    }
  }, [navList?.length]);
  useEffect(() => {
    // 获取 semi-chat-inner 元素
    const element = document.querySelector('.semi-chat-inner');
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const { height: semiChatInnerHeight } = entry.contentRect;
          const windowHeight = window.innerHeight;
          const containerHeight = windowHeight - 140 - semiChatInnerHeight;
          setContainerHeight(containerHeight);
          if (text2ImgRef.current) {
            text2ImgRef.current.style.height = `${containerHeight}px`;
          }
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
    return () => {};
  }, []);
  useEffect(() => {
    if (activeTab) {
      fetchImageData(activeTab);
    }
  }, [activeTab]);

  // 根据图片的宽高比计算高度，宽度固定是194px，得到整数，如何是NaN，返回固定279高度
  const displayHeight = (item: ImagesType) => {
    const height = Math.round((item.height / item.width) * 194);
    return height && !isNaN(height) ? height : 279;
  };

  // 优化的图片数据获取函数，支持分批渲染和状态管理
  const fetchImageData = React.useCallback(
    async (routeId: string) => {
      try {
        setLoading(true);
        // 立即清空可见图片，避免显示旧数据
        setVisibleImages([]);

        // 更新当前渲染的tab标识
        currentRenderingTabRef.current = routeId;

        dispatch({
          type: 'chat/setImageRouteId',
          payload: activeTab,
        });

        const res = await fetchTextImageHistory({ routeId });

        // 检查请求完成时是否还是当前tab
        if (currentRenderingTabRef.current !== routeId) {
          return;
        }

        if (res.data && res.data.length > 0) {
          const newResList = res.data.map((item) => {
            return {
              id: item.id + '',
              url: item.coverImage.url,
              width: item.coverImage.width,
              height: displayHeight(item.coverImage),
              name: item.coverImage.name,
              tags: [item.coverImage.name],
              images: item.images.map((item) => item.url),
            };
          });

          // 再次检查是否还是当前tab
          if (currentRenderingTabRef.current === routeId) {
            setImages(newResList);
            renderImagesBatch(newResList, true, routeId);
          }
        } else {
          if (currentRenderingTabRef.current === routeId) {
            setImages([]);
            setVisibleImages([]);
          }
        }
      } catch (error) {
        console.error('Failed to fetch images:', error);
        if (currentRenderingTabRef.current === routeId) {
          setImages([]);
          setVisibleImages([]);
        }
      } finally {
        if (currentRenderingTabRef.current === routeId) {
          setLoading(false);
        }
      }
    },
    [activeTab, dispatch, renderImagesBatch],
  );
  const handleTabChange = React.useCallback(
    (key: string) => {
      // 立即更新当前tab标识
      setCurrentTabId(key);

      // 立即设置loading状态，防止显示旧数据
      setLoading(true);

      // 立即清空当前显示的图片，避免显示旧数据
      setVisibleImages([]);

      // 清除渲染定时器
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }

      onTabChange(key);
      text2ImgRef.current?.scrollTo(0, 0);
      const currentTabData = navList.find((item) => item.routeId === Number(key));
      const richTextValue = convertTextToRichInput(currentTabData?.prompt || '');
      dispatchInUtils({
        type: 'chat/setRichTextContent',
        payload: {
          useRichText: true,
          richTextValue,
        },
      });
    },
    [onTabChange, navList],
  );

  const deleteTextImage = React.useCallback(
    async (id: string) => {
      try {
        const { data } = await deleteTextImageHistory({ id: Number(id) });
        if (data) {
          // 同时更新图片列表和可见图片列表
          const updatedImages = images.filter((item) => item.id !== id);
          const updatedVisibleImages = visibleImages.filter((item) => item.id !== id);

          setImages(updatedImages);
          setVisibleImages(updatedVisibleImages);

          Toast.success('删除成功');
        }
      } catch (error) {
        console.error('Delete failed:', error);
        Toast.error('删除失败，请重试');
      }
    },
    [images, visibleImages],
  );

  // 滚动性能优化处理
  const handleScroll = React.useCallback(() => {
    if (!isScrolling) {
      setIsScrolling(true);
    }

    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
  }, [isScrolling]);

  // 添加滚动监听
  React.useEffect(() => {
    const container = text2ImgRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true });
      return () => {
        container.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [handleScroll]);

  // 渲染内容的优化函数
  const renderContent = React.useMemo(() => {
    // 如果当前显示的tab与实际activeTab不匹配，显示loading
    if (currentTabId !== activeTab) {
      return (
        <div className={styles.loadingContainer}>
          <div className={styles.loadingText}>正在加载图片...</div>
        </div>
      );
    }

    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <div className={styles.loadingText}>正在加载图片...</div>
        </div>
      );
    }

    if (images.length === 0 && !loading) {
      return (
        <div className={styles.emptyMessageContainer} style={{ height: containerHeight - 100 }}>
          <img className={styles.emptyImage} src={EmptyImage} />
          <div className={styles.emptyMessageText}>暂无历史记录</div>
        </div>
      );
    }

    return (
      <div className={isScrolling ? styles.scrollingContainer : ''}>
        <ImgMasonry images={visibleImages} onDelete={deleteTextImage} />
      </div>
    );
  }, [loading, images, visibleImages, deleteTextImage, activeTab, currentTabId]);

  useEffect(() => {
    return () => {
      if (renderTimerRef.current) {
        clearTimeout(renderTimerRef.current);
      }
    };
  }, []);

  return (
    <div ref={text2ImgRef} className={styles.text2Img}>
      <Tabs type="line" activeKey={activeTab} onChange={handleTabChange} collapsible>
        {(navList || []).map((item: TextImageMenuList) => (
          <TabPane tab={item.routeValue} itemKey={String(item.routeId)} key={item.routeId}>
            <p className={styles.recentRecordTitle}>最近记录</p>
            {renderContent}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default Text2Img;
